


export type TaskStatus = 'pending' | 'in-progress' | 'completed' | 'error';
export type TaskProcessingStage = 
  | 'QUEUED'
  | 'CODING' 
  | 'CODE_GENERATED'
  | 'LINTING_FORMATTING' 
  | 'SECURITY_ANALYSIS' 
  | 'BUG_CHECKING' 
  | 'BUGS_ANALYZED'
  | 'REFACTORING'
  | 'AWAITING_BUG_RECHECK' 
  | 'CONTEXT_UPDATING'
  | 'CONTEXT_UPDATED'
  | 'TEST_PLANNING_INITIALIZING' 
  | 'TEST_PLAN_GENERATING' 
  | 'TEST_PLAN_GENERATED' 
  | 'TEST_CODE_INITIALIZING' 
  | 'TEST_CODE_WRITING' 
  | 'TEST_CODE_GENERATED' 
  | 'TEST_ANALYSIS_INITIALIZING' 
  | 'TEST_ANALYSIS_RUNNING' 
  | 'TEST_ANALYSIS_COMPLETED' 
  | 'IMAGE_GENERATING' 
  | 'IMAGE_GENERATED'  
  | 'CLARIFICATION_NEEDED' 
  | 'CLARIFICATION_PROVIDED' 
  | 'DONE'
  | 'FAILED';

export interface TaskAgentMessage {
  id: string;
  timestamp: Date;
  agent: string;
  message: string;
  status: AgentLog['status'];
  stage?: TaskProcessingStage; 
  subDetailSections?: Array<{ 
    title: string;
    content: string;
    isCodeBlock?: boolean;
  }>;
}

export interface BugInfo {
  filePath: string; 
  bugId: string; 
  description: string; 
  severity: 'low' | 'medium' | 'high' | 'critical'; 
  attempts: number; 
  lineNumber?: number; 
  originalTestFailure?: { 
    testFilePath: string;
    testName: string;
    errorMessage: string;
  };
  isSecurityIssue?: boolean; 
}
export interface Task {
  id: string;
  description: string;
  status: TaskStatus;
  currentProcessingStage: TaskProcessingStage;
  details?: string; 
  purpose?: string; 
  code?: string; 
  error?: string; 
  agentMessages: TaskAgentMessage[]; 
  identifiedBugs: BugInfo[]; 
  bugFixingCycles: number; 
  unresolvedBugs: BugInfo[]; 
  relatedSourceFiles?: string[]; 
  clarificationQuestion?: string; 
  clarifierResponse?: string; 
  originalStageBeforeClarification?: TaskProcessingStage; 
  currentRefactoringBugId?: string; 
  
  priority?: 'low' | 'medium' | 'high';
  dependencies?: string[]; 
  estimatedComplexity?: 'low' | 'medium' | 'high' | 'unknown';
}

export interface FileNode {
  id: string;
  name: string;
  type: 'file' | 'folder';
  path: string;
  content?: string;
  children?: FileNode[];
  isTestFile?: boolean; 
}

export interface AgentLog {
  id:string;
  timestamp: Date;
  agent: string; 
  message: string;
  status: 'info' | 'success' | 'error' | 'working';
  taskId?: string; 
}

export interface ModelInfo {
  id: string;
  name: string;
  rpm: string;
  tpm: string;
  rpd: string;
  description?: string; 
}

export enum AgentType {
  PLANNER = 'PLANNER', // Also used for Plan Review and Build Validation by varying system instruction
  CODER = 'CODER',
  LINTER_FORMATTER = 'LINTER_FORMATTER',
  SECURITY_ANALYST = 'SECURITY_ANALYST',
  BUG_HUNTER = 'BUG_HUNTER',
  REFACTORER = 'REFACTORER',
  CONTEXT_MANAGER = 'CONTEXT_MANAGER',
  TESTER = 'TESTER', // Covers Test Planning, Test Code Gen, Test Analysis, Test Coverage
  IMAGE_GENERATOR = 'IMAGE_GENERATOR',
  CLARIFIER = 'CLARIFIER', 
}

export enum OperatingPhase {
  AGENT_MODEL_CONFIGURATION = 'AGENT_MODEL_CONFIGURATION',
  API_KEY_INPUT = 'API_KEY_INPUT',
  COMPANY_OPERATIONAL = 'COMPANY_OPERATIONAL',
}

export enum ProjectLifecyclePhase {
  IDLE = 'IDLE', 
  AWAITING_PROJECT_IDEA = 'AWAITING_PROJECT_IDEA',
  AWAITING_LICENSE_CHOICE = 'AWAITING_LICENSE_CHOICE', 
  PLANNING = 'PLANNING', 
  PLAN_REVIEW_PENDING = 'PLAN_REVIEW_PENDING', 
  PLANNING_USER_FEEDBACK = 'PLANNING_USER_FEEDBACK', 
  AUTONOMOUS_EXECUTION = 'AUTONOMOUS_EXECUTION', 
  TESTING_PLANNING = 'TESTING_PLANNING', 
  TEST_CODE_GENERATION_PENDING = 'TEST_CODE_GENERATION_PENDING', 
  TEST_ANALYSIS_PENDING = 'TEST_ANALYSIS_PENDING', 
  AWAITING_TEST_COVERAGE_ANALYSIS = 'AWAITING_TEST_COVERAGE_ANALYSIS', 
  PROJECT_VERIFIED = 'PROJECT_VERIFIED', 
  PROJECT_BUILD_VALIDATION = 'PROJECT_BUILD_VALIDATION', 
  PROJECT_READY_FOR_DEPLOYMENT = 'PROJECT_READY_FOR_DEPLOYMENT', 
  AWAITING_RATE_LIMIT_RESOLUTION = 'AWAITING_RATE_LIMIT_RESOLUTION',
  PROJECT_PAUSED_ON_RATE_LIMIT = 'PROJECT_PAUSED_ON_RATE_LIMIT',
  EXECUTION_HALTED_ERROR = 'EXECUTION_HALTED_ERROR',
}

export interface RateLimitInfo {
  agentType: AgentType;
  modelId: string;
  errorMessage: string; 
  proposedAlternativeModelId?: string;
}

export interface UserFeedback {
  type: 'bug' | 'feature' | 'change';
  filePath?: string;
  description: string;
}

export interface DecisionLogEntry {
  id: string;
  agent: AgentType | 'System' | 'ProjectManager' | 'User'; 
  action: string; 
  details: string; 
  reason?: string; 
  timestamp: Date;
  taskId?: string; 
}


export interface DevNote {
  id: string;
  note: string; 
  relatedFilePath?: string; 
  timestamp: Date;
  source: 'BugRepetition' | 'Manual' | 'RefactorerSuggestion' | 'SystemObservation' | 'SecurityAnalyst';
}

export interface RepeatedBugPattern {
  id: string; 
  filePath: string;
  simplifiedDescription: string; 
  count: number;
  lastReportedTimestamp: Date;
  devNoteGenerated: boolean; 
  exampleFullDescription: string; 
}

export interface UserAuthorshipDetails {
  fullName: string;
  email: string;
  website?: string;
  copyrightYear: string;
}

export enum LicenseType {
  MIT = 'MIT',
  GPLv3 = 'GPLv3',
  Apache2 = 'Apache-2.0',
  Proprietary = 'Proprietary',
  Unspecified = 'Unspecified',
}

export interface LicenseInfo {
  type: LicenseType;
  authorship?: UserAuthorshipDetails; 
}


export interface ProjectContext {
  id: string; 
  name: string; 
  idea: string;
  fullContext: string; 
  apiKey: string | null;
  tasks: Task[];
  fileStructure: FileNode[];
  companyLogs: AgentLog[]; 
  currentPhase: OperatingPhase; 
  projectLifecycle: ProjectLifecyclePhase; 
  errorMessage?: string;
  currentFilePreview: FileNode | null;
  activeTaskId: string | null; 
  availableModels: ModelInfo[];
  agentModelConfiguration: Record<AgentType, string>;
  rateLimitInfo?: RateLimitInfo;
  testingCycleCount: number; 
  lastUserFeedback?: UserFeedback;
  
  architecturalNotes?: string;
  dependencyGraphNotes?: string; 
  decisionLog: DecisionLogEntry[];
  
  initialPlanForReview?: {
    tasks: GeminiJsonPlannerTask[];
    fileStructure: Array<Omit<FileNode, 'id' | 'path' | 'children' | 'isTestFile'> & { children?: Array<Omit<FileNode, 'id' | 'path' | 'isTestFile'>> }>;
    technologyStackSuggestion?: string;
  };
  
  devNotes: DevNote[];
  repeatedBugPatterns: RepeatedBugPattern[];
  suggestedTechnologyStack?: string;
  architecturalSuggestions: string[];
  lastModified: string; 
  licenseInfo?: LicenseInfo; 
}

export interface SavedProjectEntry {
  id: string;
  name: string;
  lastModified: string; 
  ideaSnippet: string; 
  taskCount: number;
  completedTaskCount: number;
}


export interface GeminiJsonPlannerTask {
  id?: string; 
  description: string;
  details?: string; 
  
  priority?: 'low' | 'medium' | 'high';
  dependencies?: string[]; 
  estimatedComplexity?: 'low' | 'medium' | 'high' | 'unknown';
}
export interface GeminiJsonPlannerResponse {
  tasks: Array<GeminiJsonPlannerTask>;
  fileStructure?: Array<Omit<FileNode, 'id' | 'path' | 'children' | 'isTestFile'> & { children?: Array<Omit<FileNode, 'id' | 'path' | 'isTestFile'>> }>;
  reviewNotes?: string; 
  technologyStackSuggestion?: string; 
}

export interface GeminiJsonCodeResponse {
  code: string;
  clarificationQuestion?: string; 
}

export interface GeminiJsonBugReportResponse {
  bugs: BugInfo[]; 
}

export interface GeminiJsonRefactorResponse { 
  fileChanges: Array<{ filePath: string; fixedCode: string }>;
  explanation?: string;
  clarificationQuestion?: string; 
}

export interface GeminiJsonContextUpdateResponse {
  updatedContext: string; 
  
}

export interface TestFileDescription {
  filePath: string; 
  description: string; 
  relatedSourceFiles: string[]; 
}

export interface GeminiJsonTestPlanResponse {
  testFiles: TestFileDescription[];
  overallStrategy?: string; 
}


export interface GeminiJsonTestAnalysisResponse {
  issues: BugInfo[]; 
  summary?: string; 
}


export interface GeminiJsonSingleTestFailureAnalysisResponse {
  analyzedBugInfo: BugInfo;
}

export interface TestResult {
  suiteName: string;
  testName: string;
  passed: boolean;
  error?: { message: string; stack?: string };
  duration: number;
}


export interface ImageBytes {
  imageBytes: string; 
  mimeType: string;   
}

export interface GeneratedImage {
  image: ImageBytes;
  seed?: number;
  finishReason?: string;
  finishMessage?: string;
}

export interface GeminiJsonImageGenerationResponse {
  generatedImages: GeneratedImage[];
}

export interface GeminiJsonClarifierResponse {
  answer: string;
  confidence?: number; 
}

export interface GeminiJsonLintFormatResponse { 
  lintedCode: string;
  explanation?: string;
}

export interface GeminiJsonBuildValidationResponse {
  buildIssues: BugInfo[]; // Re-using BugInfo for simplicity, filePath will be e.g. package.json
  buildCommand?: string; // e.g., "npm run build"
  projectType?: string; // e.g., "React SPA (Vite + TypeScript)"
  validationSummary: string; // Textual summary
}