
import { GoogleGenAI, GenerateContentResponse } from "@google/genai";
import { 
  GeminiJsonPlannerResponse, 
  GeminiJsonCodeResponse, 
  GeminiJsonBugReportResponse,
  GeminiJsonRefactorResponse,
  GeminiJsonContextUpdateResponse,
  GeminiJsonTestPlanResponse,
  GeminiJsonTestAnalysisResponse, 
  GeminiJsonSingleTestFailureAnalysisResponse, 
  FileNode,
  BugInfo, 
  UserFeedback,
  GeminiJsonPlannerTask,
  ImageBytes, 
  GeneratedImage, 
  // GeminiJsonImageGenerationResponse, // Not directly used as output type for generateImageViaGemini
  GeminiJsonClarifierResponse, 
  ProjectContext,
  GeminiJsonLintFormatResponse,
  GeminiJsonBuildValidationResponse,
  LicenseInfo,
  LicenseType,
  UserAuthorshipDetails
} from '../types';
import { 
  SYSTEM_INSTRUCTION_PLANNER,
  SYSTEM_INSTRUCTION_CODER,
  SYSTEM_INSTRUCTION_BUG_HUNTER,
  SYSTEM_INSTRUCTION_REFACTORER,
  SYSTEM_INSTRUCTION_CONTEXT_MANAGER,
  SYSTEM_INSTRUCTION_TEST_PLANNER,
  SYSTEM_INSTRUCTION_TEST_CODER,
  SYSTEM_INSTRUCTION_TEST_ANALYZER, 
  SYSTEM_INSTRUCTION_FEEDBACK_TASK_GENERATOR,
  SYSTEM_INSTRUCTION_IMAGE_GENERATOR, 
  SYSTEM_INSTRUCTION_CLARIFIER,
  SYSTEM_INSTRUCTION_TEST_COVERAGE_ANALYZER,
  SYSTEM_INSTRUCTION_PLAN_REVIEWER,
  SYSTEM_INSTRUCTION_LINTER_FORMATTER,
  SYSTEM_INSTRUCTION_SECURITY_ANALYST,
  SYSTEM_INSTRUCTION_BUILD_VALIDATOR,
  MIT_LICENSE_TEXT
} from '../constants';
import { v4 as uuidv4 } from 'uuid';


class JsonParseError extends Error {
  public rawText: string;
  public attemptedJsonStr: string;
  public parserErrorMessage: string;

  constructor(message: string, rawText: string, attemptedJsonStr: string, parserErrorMessage: string) {
    super(message);
    this.name = "JsonParseError";
    this.rawText = rawText; 
    this.attemptedJsonStr = attemptedJsonStr; 
    this.parserErrorMessage = parserErrorMessage; 
  }
}

export class RateLimitError extends Error {
  public modelId: string;
  constructor(message: string, modelId: string) {
    super(message);
    this.name = "RateLimitError";
    this.modelId = modelId;
  }
}

interface TestResult {
  suiteName: string;
  testName: string;
  passed: boolean;
  error?: { message: string; stack?: string };
  duration: number;
}

// Helper function for validating GeminiJsonPlannerTask with new optional fields
const isValidGeminiJsonPlannerTask = (task: any): task is GeminiJsonPlannerTask => {
  return typeof task === 'object' && task !== null &&
    typeof task.description === 'string' &&
    (typeof task.details === 'undefined' || typeof task.details === 'string') &&
    (typeof task.id === 'undefined' || typeof task.id === 'string') &&
    (typeof task.priority === 'undefined' || ['low', 'medium', 'high'].includes(task.priority)) &&
    (typeof task.dependencies === 'undefined' || (Array.isArray(task.dependencies) && task.dependencies.every((dep: any) => typeof dep === 'string'))) &&
    (typeof task.estimatedComplexity === 'undefined' || ['low', 'medium', 'high', 'unknown'].includes(task.estimatedComplexity));
};


export class GeminiService {
  private ai: GoogleGenAI;
  private readonly DEFAULT_MAX_JSON_RETRIES = 2; 
  private readonly RATE_LIMIT_MAX_RETRIES = 3; 
  private readonly INITIAL_BACKOFF_MS = 3000; 
  private readonly MAX_BACKOFF_MS = 60000;
  private readonly MAX_GENERAL_RETRIES_FOR_IMAGE_GENERATION = 2; 


  constructor(apiKey: string) {
    if (!apiKey) {
      throw new Error("API Key is required to initialize GeminiService.");
    }
    this.ai = new GoogleGenAI({ apiKey });
  }

  private parseJsonText(responseText: string): any {
    let jsonStr = responseText.trim();
    
    const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
    const match = jsonStr.match(fenceRegex);
    if (match && match[2]) {
      jsonStr = match[2].trim();
    }

    if (jsonStr.endsWith("```")) {
      jsonStr = jsonStr.slice(0, -3).trim();
    }

    let firstChar = -1;
    let lastChar = -1;
    let openChar: '{' | '[' | null = null;
    let closeChar: '}' | ']' | null = null;

    for (let i = 0; i < jsonStr.length; i++) {
        if (jsonStr[i] === '{') {
            firstChar = i;
            openChar = '{';
            closeChar = '}';
            break;
        }
        if (jsonStr[i] === '[') {
            firstChar = i;
            openChar = '[';
            closeChar = ']';
            break;
        }
    }
    
    let parseCandidateStr = jsonStr; 

    if (firstChar === -1 || !openChar || !closeChar) {
        // No clear JSON start, try to parse as is
    } else {
        let balance = 0;
        let inString = false;
        for (let i = firstChar; i < jsonStr.length; i++) {
            const char = jsonStr[i];

            if (char === '"') {
                // This is a simplified check; JSON.parse handles complex escapes.
                // We only care about not mistaking quotes inside strings for structural quotes.
                if (i > 0 && jsonStr[i-1] !== '\\') { 
                    inString = !inString;
                } else if (i === 0) { 
                    inString = !inString;
                }
            }

            if (!inString) {
                if (char === openChar) {
                    balance++;
                } else if (char === closeChar) {
                    balance--;
                }
            }

            if (balance === 0 && i >= firstChar) {
                lastChar = i;
                break;
            }
        }
        
        if (lastChar !== -1) {
            parseCandidateStr = jsonStr.substring(firstChar, lastChar + 1);
        } else {
            console.warn("Could not find balanced JSON structure, attempting to parse potentially partial or malformed JSON string (from first bracket/brace):", jsonStr.substring(firstChar, Math.min(firstChar + 200, jsonStr.length)));
            parseCandidateStr = jsonStr.substring(firstChar); 
        }
    }

    try {
      return JSON.parse(parseCandidateStr);
    } catch (e) {
      const specificParserError = e instanceof Error ? e.message : String(e);
      console.error(
        "Failed to parse JSON text. Specific Error:", specificParserError, 
        "\nOriginal response text (first 500 chars):", responseText.substring(0, 500), 
        "\nString attempted for parsing (first 500 chars):", parseCandidateStr.substring(0, 500)
      );
      throw new JsonParseError(
        `JSON parsing failed. Problematic string (first 200 chars): ${parseCandidateStr.substring(0,200)}... Specific error: ${specificParserError}`, 
        responseText, 
        parseCandidateStr, 
        specificParserError
      );
    }
  }

  private parseAndValidateJsonResponse<T>(response: GenerateContentResponse, schemaValidator: (data: any) => data is T, rawTextFromResponse?: string): T {
    const textToParse = rawTextFromResponse || response.text;
    const parsedData = this.parseJsonText(textToParse);

    if (schemaValidator(parsedData)) {
      return parsedData as T;
    } else {
      console.error("Parsed JSON does not match expected schema. Parsed data:", parsedData, "Original response text:", textToParse);
      throw new JsonParseError(
        "AI response JSON parsed but does not match expected data structure.", 
        textToParse,
        JSON.stringify(parsedData), 
        "Schema validation failed after successful parsing." 
      );
    }
  }

  private serializeFileStructureForPrompt(fileStructure: FileNode[], includeContent: boolean = false): string {
    let structureString = "Project File Structure Overview:\n";
    const traverse = (nodes: FileNode[], depth: number) => {
      for (const node of nodes) {
        let typeInfo = node.type;
        if (node.isTestFile) typeInfo += ' (test file)';
        
        structureString += `${"  ".repeat(depth)}- ${node.path} (${typeInfo}${node.type === 'file' && !node.content ? ', empty' : node.type === 'file' && node.content ? ', has content' : ''})\n`;
        if (includeContent && node.type === 'file' && node.content) {
            const previewContent = node.content.substring(0, 300) + (node.content.length > 300 ? "..." : "");
            structureString += `${"  ".repeat(depth+1)}Content Preview:\n${"  ".repeat(depth+2)}${previewContent.split('\n').join(`\n${"  ".repeat(depth+2)}`)}\n`;
        }
        if (node.children && node.children.length > 0) {
          traverse(node.children, depth + 1);
        }
      }
    };
    traverse(fileStructure, 0);
    return structureString;
  }

   private serializePlannerTasksForPrompt(tasks: GeminiJsonPlannerTask[]): string {
    if (!tasks || tasks.length === 0) {
      return "No tasks in the initial plan.\n";
    }
    let tasksString = "Initial Plan - Tasks Overview:\n";
    tasks.forEach((task, index) => {
      tasksString += `${index + 1}. Description: ${task.description}\n`;
      tasksString += `   Details (File Path): ${task.details || 'N/A'}\n`;
      if(task.priority) tasksString += `   Priority: ${task.priority}\n`;
      if(task.dependencies && task.dependencies.length > 0) tasksString += `   Dependencies: ${task.dependencies.join(', ')}\n`;
      if(task.estimatedComplexity) tasksString += `   Complexity: ${task.estimatedComplexity}\n`;
    });
    return tasksString;
  }


  private async makeRequestWithRetry<T>(
    modelName: string, 
    originalPrompt: string,
    systemInstruction: string,
    schemaValidator: (data: any) => data is T,
    temperature: number = 0.5,
    isCodeGenerationType: boolean = false 
  ): Promise<T> {
    let currentAttempt = 0; // Number of attempts made so far (0 for initial, 1 for first retry, etc.)
    let lastError: Error | JsonParseError | RateLimitError | null = null;

    let maxRetriesForCurrentErrorType = this.DEFAULT_MAX_JSON_RETRIES;

    while (currentAttempt <= maxRetriesForCurrentErrorType) {
      try {
        let promptForThisAttempt = originalPrompt;
        if (currentAttempt > 0 && lastError instanceof JsonParseError) { // This is a retry for JSON error
          console.warn(`Retry ${currentAttempt}/${maxRetriesForCurrentErrorType} for JSON processing with model ${modelName}. Requesting AI to correct previous malformed JSON or schema.`);
          const specificErrorMsg = lastError.parserErrorMessage;
          const problematicString = lastError.attemptedJsonStr; 
          
          let jsonIssueExplanation = "";
          let detectedInvalidStructure: any = null;
          try {
            detectedInvalidStructure = JSON.parse(problematicString); 
          } catch (e) { /* ignore if reparsing the problematic string itself fails */ }

          if (specificErrorMsg.includes("Schema validation failed")) {
              jsonIssueExplanation = "\nThe JSON was syntactically valid but did not match the expected data structure (schema).";
              if (isCodeGenerationType) { 
                  jsonIssueExplanation += "\nFor responses like code generation or linting, the expected JSON format is typically `{\"code\": \"your_generated_code_string\"}` or `{\"lintedCode\": \"...\"}`. If a clarification is needed by the Coder, it's `{\"code\": \"...\", \"clarificationQuestion\": \"...\"}`.";
                  if (detectedInvalidStructure && typeof detectedInvalidStructure === 'object' && 'files' in detectedInvalidStructure && Array.isArray(detectedInvalidStructure.files)) {
                      jsonIssueExplanation += "\nCRITICAL: Your previous response incorrectly used a `{\"files\": [...]}` structure. This is WRONG for single file operations (like Coder or Linter). You MUST return `{\"code\": \"...\"}` or similar for the ONE file specified in the request.";
                  } else if (detectedInvalidStructure && Array.isArray(detectedInvalidStructure)) {
                      jsonIssueExplanation += "\nCRITICAL: Your previous response was an array (e.g., `[{\"code\": \"...\"}]`). For single file operations, please return a single JSON object: `{\"code\": \"...\"}` or similar.";
                  }
              }
          } else { 
              jsonIssueExplanation = `
This means the JSON syntax itself was invalid. Common syntax errors include:
- Missing commas between elements or properties.
- Unmatched brackets [] or braces {}.
- **CRITICAL FOR STRING VALUES (like 'code', 'description', 'filePath', 'explanation', 'updatedContext', 'lintedCode', etc.): Incorrectly escaped special characters.**
  - Literal double quotes (\") inside strings MUST be escaped as \\".
  - Literal backslashes (\\) inside strings MUST be escaped as \\\\.
  - Newlines inside strings MUST be escaped as \\n. (Example: "line1\\nline2")
  - Tabs inside strings MUST be escaped as \\t. (Example: "item1\\titem2")
  - Other special characters like carriage return (\\r), form feed (\\f), and backspace (\\b) must also be correctly escaped if present.
Review the problematic JSON snippet and the specific error message ("${specificErrorMsg}") carefully to identify and fix the fundamental JSON syntax error.
The JSON must be PURE and STRICT, starting with { and ending with }, with no other text or markdown.`;
          }
          
          let errorContextSnippet = "";
          const errorPositionMatch = specificErrorMsg.match(/at position (\d+)/);
          const errorPosition = errorPositionMatch ? parseInt(errorPositionMatch[1], 10) : -1;
          const snippetRadius = 150; // Characters around the error position

          if (errorPosition !== -1 && problematicString.length > (snippetRadius * 2)) { // Only add if string is large enough and position is known
            const start = Math.max(0, errorPosition - snippetRadius);
            const end = Math.min(problematicString.length, errorPosition + snippetRadius);
            errorContextSnippet = `\n\nContext around error (approx. position ${errorPosition}):\n---\n...${problematicString.substring(start, end)}...\n---`;
          }


          promptForThisAttempt = `
            Your previous response to the original request resulted in a JSON processing issue.
            Original Request (Summary):
            ---
            ${originalPrompt.substring(0, 300)}... 
            ---
            Problematic JSON data (or snippet) from your previous attempt (string that caused parsing error):
            ---
            ${problematicString.substring(0, 800)} 
            ---
            ${errorContextSnippet}
            The SPECIFIC ISSUE encountered was: "${specificErrorMsg}"
            ${jsonIssueExplanation}

            Please re-evaluate the original request. Your entire response MUST be a single, perfectly valid JSON object that strictly adheres to all JSON syntax rules and matches the expected data structure for the task. Pay EXTREME attention to correctly escaping all special characters within string values. Do not include any explanatory text or markdown outside of the JSON object itself.
          `;
        } else if (currentAttempt > 0 && lastError) { 
             console.warn(`Retry ${currentAttempt}/${maxRetriesForCurrentErrorType} with model ${modelName} due to error: ${lastError.message}. Retrying.`);
        }

        const response = await this.ai.models.generateContent({
          model: modelName,
          contents: promptForThisAttempt,
          config: {
            systemInstruction: systemInstruction,
            responseMimeType: "application/json",
            temperature: temperature,
          }
        });
        
        const rawTextFromResponse = response.text;

        if (isCodeGenerationType) { 
            const parsedData = this.parseJsonText(rawTextFromResponse);
            let objectToValidate: any = null;

            if (Array.isArray(parsedData)) {
                if (parsedData.length > 0 && typeof parsedData[0] === 'object' && parsedData[0] !== null && 
                    ( ('code' in parsedData[0] && typeof parsedData[0].code === 'string') || 
                      ('lintedCode' in parsedData[0] && typeof parsedData[0].lintedCode === 'string') ) ) {
                    console.warn(`AI returned an array of code/lint objects for model ${modelName}, using the first element.`);
                    objectToValidate = parsedData[0];
                } else if (parsedData.length === 0) {
                     throw new JsonParseError(`AI returned an empty array. Expected a code/lint object.`, rawTextFromResponse, "[]", "Expected non-empty array with code/lint object or a single code/lint object.");
                } else {
                    throw new JsonParseError(`AI returned an array, but its first element is not a valid code/lint object.`, rawTextFromResponse, JSON.stringify(parsedData), "Array element schema mismatch for code/lint object.");
                }
            } else {
                objectToValidate = parsedData;
            }

            if (schemaValidator(objectToValidate)) { 
                return objectToValidate as T;
            }
            const expectedSchemaMessage = (systemInstruction === SYSTEM_INSTRUCTION_CODER) 
                ? `e.g., {"code": "...", "clarificationQuestion?": "..."}` 
                : (systemInstruction === SYSTEM_INSTRUCTION_LINTER_FORMATTER)
                ? `e.g., {"lintedCode": "...", "explanation?": "..."}`
                : `the expected JSON structure.`;

            throw new JsonParseError(`AI response JSON does not match expected schema (${expectedSchemaMessage}). Received: ${JSON.stringify(objectToValidate).substring(0,200)}`, rawTextFromResponse, JSON.stringify(objectToValidate), "Schema validation failed for code/lint object.");
        }
        return this.parseAndValidateJsonResponse<T>(response, schemaValidator, rawTextFromResponse);

      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error(`Attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1} with model ${modelName} failed:`, err.message);
        lastError = err;
        
        if (err.message && err.message.includes("429") && (err.message.toLowerCase().includes("resource_exhausted") || err.message.toLowerCase().includes("rate limit"))) {
            maxRetriesForCurrentErrorType = this.RATE_LIMIT_MAX_RETRIES; 
            console.warn(`Rate limit detected with model ${modelName}. Will retry up to ${maxRetriesForCurrentErrorType + 1} total times with increasing backoff.`);
            if (currentAttempt >= maxRetriesForCurrentErrorType) { 
                throw new RateLimitError(lastError.message, modelName);
            }
        } else if (err instanceof JsonParseError) {
            maxRetriesForCurrentErrorType = this.DEFAULT_MAX_JSON_RETRIES;
             if (currentAttempt >= maxRetriesForCurrentErrorType) {
                throw lastError;
            }
        } else { // Other errors
            maxRetriesForCurrentErrorType = 0; // No retries for unknown errors by default or fewer
             if (currentAttempt >= maxRetriesForCurrentErrorType) {
                throw lastError;
            }
        }
        
        currentAttempt++;
        let delay = this.INITIAL_BACKOFF_MS;
        if (maxRetriesForCurrentErrorType === this.RATE_LIMIT_MAX_RETRIES) { 
            delay = Math.min(this.MAX_BACKOFF_MS, this.INITIAL_BACKOFF_MS * Math.pow(2, currentAttempt -1)); // currentAttempt is 1-based for retries here
            delay += Math.random() * 1000; 
            console.log(`Rate limit backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
        } else if (lastError instanceof JsonParseError) { 
            delay = Math.min(this.MAX_BACKOFF_MS / 2, (this.INITIAL_BACKOFF_MS / 2) * Math.pow(2, currentAttempt -1));
            delay += Math.random() * 500; 
            console.log(`JSON error/schema backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
        } else { 
            delay = Math.min(this.MAX_BACKOFF_MS / 3, (this.INITIAL_BACKOFF_MS / 3) * Math.pow(2, currentAttempt -1));
            delay += Math.random() * 500;
            console.log(`General error backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
        }
        
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
    // If loop finishes, it means all retries were exhausted for the last error type
    if (lastError instanceof RateLimitError) throw lastError; // Already a RateLimitError
    if (lastError?.message.includes("429")) throw new RateLimitError(lastError.message, modelName); // Convert to RateLimitError if it was one
    throw lastError || new Error(`Exhausted retries for AI request with model ${modelName}. Unknown error.`);
  }

  async generateInitialPlan(
    projectIdea: string, 
    modelName: string,
    projectContext?: string, 
    licenseInfo?: LicenseInfo   
  ): Promise<GeminiJsonPlannerResponse> {
    let originalPrompt = `Project Idea: "${projectIdea}"`;
    if (projectContext) {
      originalPrompt += `\n\nFull Project Context (for awareness, includes idea and license info if set):\n${projectContext}`;
    }
    if (licenseInfo) {
      originalPrompt += `\n\nLicense Information for this Project:
Type: ${licenseInfo.type}`;
      if (licenseInfo.type === LicenseType.Proprietary && licenseInfo.authorship) {
        originalPrompt += `
Author: ${licenseInfo.authorship.fullName}
Year: ${licenseInfo.authorship.copyrightYear}
Contact: ${licenseInfo.authorship.email}
${licenseInfo.authorship.website ? `Website: ${licenseInfo.authorship.website}` : ''}`;
      }
       originalPrompt += `\n(Ensure a task is created for the LICENSE file based on this.)`;
    } else {
        originalPrompt += `\n\nLicense Information: Not specified yet or default (e.g. MIT if not proprietary). Ensure a generic LICENSE file task if appropriate.`;
    }

    return this.makeRequestWithRetry<GeminiJsonPlannerResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_PLANNER,
      (data): data is GeminiJsonPlannerResponse => {
        return typeof data === 'object' && data !== null && 
               'tasks' in data && Array.isArray(data.tasks) && 
               ('fileStructure' in data ? Array.isArray(data.fileStructure) : true) && 
               data.tasks.every(isValidGeminiJsonPlannerTask) &&
               (!data.fileStructure || data.fileStructure.every((f: any) => typeof f === 'object' && f !== null && 'name' in f && 'type' in f)) &&
               (data.technologyStackSuggestion === null || typeof data.technologyStackSuggestion === 'undefined' || typeof data.technologyStackSuggestion === 'string');
      },
      0.3
    );
  }

  async reviewAndRefinePlan(
    projectIdea: string,
    initialTasks: GeminiJsonPlannerTask[], 
    initialFileStructure: Array<Omit<FileNode, 'id' | 'path' | 'children' | 'isTestFile'> & { children?: Array<Omit<FileNode, 'id' | 'path' | 'isTestFile'>> }>, 
    projectContext: string, 
    modelName: string,
    currentTechnologyStackSuggestion?: string 
  ): Promise<GeminiJsonPlannerResponse> {
    const initialTasksPrompt = this.serializePlannerTasksForPrompt(initialTasks);
    const initialFileStructurePrompt = `Initial Plan - File Structure Overview:\n${JSON.stringify(initialFileStructure, null, 2).substring(0, 2000)}...\n(Review and refine this structure if needed based on task adjustments.)`;
    const techStackPrompt = currentTechnologyStackSuggestion 
        ? `Current Technology Stack Suggestion to Review: ${currentTechnologyStackSuggestion}\n(Critically review this suggestion. If it's good, keep it or refine slightly. If it's inappropriate or missing and the idea is general, propose a new one and justify it. Ensure tasks and file structure align with the final stack.)` 
        : "No technology stack was initially suggested. If the project idea is general and one is appropriate, propose a common stack and justify it. Ensure tasks and file structure align.";

    const originalPrompt = `
      Original Project Idea: "${projectIdea}"
      ---
      Current Project Context (includes idea, license info, and any initial stack suggestion): 
      ${projectContext}
      ---
      Initial Plan to Review:
      ${initialTasksPrompt}
      ---
      ${initialFileStructurePrompt}
      ---
      ${techStackPrompt}
      ---
      Critically review this entire initial plan (tasks, file structure, and technology stack suggestion). 
      If it's good, return it as is or with minor tweaks (optionally with "reviewNotes"). 
      If it needs improvement (missing tasks, illogical structure, inappropriate/missing stack, etc.), provide a NEW, COMPLETE, and REFINED plan (new "tasks", "fileStructure", and "technologyStackSuggestion") and "reviewNotes" explaining changes.
      Ensure all essential boilerplate files are included for the project type and the (potentially new) technology stack. For tasks, ensure "id", "description", "details" are present and estimate "priority", "dependencies", and "estimatedComplexity".
      Ensure a task for the LICENSE file is present and correctly reflects the licenseInfo in the project context.
    `;
    return this.makeRequestWithRetry<GeminiJsonPlannerResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_PLAN_REVIEWER,
      (data): data is GeminiJsonPlannerResponse => {
        return typeof data === 'object' && data !== null &&
               'tasks' in data && Array.isArray(data.tasks) &&
               ('fileStructure' in data ? Array.isArray(data.fileStructure) : true) &&
               data.tasks.every(isValidGeminiJsonPlannerTask) &&
               (!data.fileStructure || data.fileStructure.every((f: any) => typeof f === 'object' && f !== null && 'name' in f && 'type' in f)) &&
               (data.reviewNotes === null || typeof data.reviewNotes === 'undefined' || typeof data.reviewNotes === 'string') &&
               (data.technologyStackSuggestion === null || typeof data.technologyStackSuggestion === 'undefined' || typeof data.technologyStackSuggestion === 'string');
      },
      0.4 
    );
  }


  async generateTasksFromUserFeedback(
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string 
  ): Promise<GeminiJsonPlannerResponse> { 
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    const originalPrompt = `
      Original Project Idea: ${projectIdea}
      User Feedback Received:
        Type: ${feedback.type}
        File Path (if any): ${feedback.filePath || 'N/A'}
        Description: ${feedback.description}
      ---
      Current Project Full Context (includes this feedback and licenseInfo): ${projectContext}
      ---
      ${fileStructurePrompt}
      ---
      Based on the user feedback, original idea, context, and file structure, generate a list of specific tasks to address the feedback. 
      For each task, include "id", "description", "details", and estimate "priority", "dependencies", and "estimatedComplexity".
      Ensure image-related feedback tasks have descriptions like "Generate image: ..." or "Update image: ..." and the filename in "details".
      If feedback pertains to licensing, generate appropriate tasks (e.g., "Update LICENSE file").
    `;
    return this.makeRequestWithRetry<GeminiJsonPlannerResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_FEEDBACK_TASK_GENERATOR,
      (data): data is GeminiJsonPlannerResponse => { 
        return typeof data === 'object' && data !== null &&
               'tasks' in data && Array.isArray(data.tasks) &&
               data.tasks.every(isValidGeminiJsonPlannerTask) && 
               !('fileStructure' in data); 
      },
      0.4
    );
  }


  async generateFileContent( 
    projectIdea: string,
    filePath: string,
    fileDescription: string,
    projectContextString: string, 
    fileStructure: FileNode[],
    modelName: string,
    licenseInfo?: LicenseInfo, 
    clarifierResponse?: string
  ): Promise<GeminiJsonCodeResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    let originalPrompt = `
      Project Idea: ${projectIdea}
      Project Context (includes licenseInfo if set): ${projectContextString} 
      ${fileStructurePrompt}
      File Path To Generate: ${filePath}
      File Description/Purpose: ${fileDescription}
    `;
     if (licenseInfo) {
        originalPrompt += `\n---
        License Information for this Project:
        Type: ${licenseInfo.type}`;
        if (licenseInfo.type === LicenseType.Proprietary && licenseInfo.authorship) {
            originalPrompt += `
        Author: ${licenseInfo.authorship.fullName}
        Copyright Year: ${licenseInfo.authorship.copyrightYear}
        (Use these details if generating a proprietary LICENSE file or copyright headers if specifically tasked).`;
        } else if (licenseInfo.type === LicenseType.MIT && licenseInfo.authorship) {
             originalPrompt += `
        Author for MIT placeholder: ${licenseInfo.authorship.fullName}
        Copyright Year for MIT placeholder: ${licenseInfo.authorship.copyrightYear}
        (Use these details to fill [fullname] and [year] in the MIT license text if generating it).`;
        }
        originalPrompt += `\n---`;
    }
    if (clarifierResponse) {
        originalPrompt += `\n---
        Previous Clarification Provided by AI Clarifier Agent for this task:
        ${clarifierResponse}
        ---
        Please use this clarification when generating the code.
        `;
    }
    originalPrompt += `\nGenerate the source code for this file. If the purpose is highly ambiguous and critical details are missing (even after potential clarification), ask a clarification question instead.
    If filePath is 'LICENSE', use the provided License Information to generate the correct license text. For MIT, use the standard text replacing [year] and [fullname]. For Proprietary, generate a copyright notice using authorship details.
    The MIT License text to use if filePath is 'LICENSE' and type is 'MIT':
    \`\`\`
    ${MIT_LICENSE_TEXT}
    \`\`\`
    `;
    
    return this.makeRequestWithRetry<GeminiJsonCodeResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_CODER, 
      (data): data is GeminiJsonCodeResponse => {
        return typeof data === 'object' && data !== null && 
               'code' in data && typeof data.code === 'string' &&
               (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
      },
      0.5,
      true 
    );
  }

  async lintAndFormatCode(
    code: string,
    filePath: string,
    projectContext: string, 
    modelName: string
  ): Promise<GeminiJsonLintFormatResponse> {
    const originalPrompt = `
      Project Context: ${projectContext} 
      File Path: ${filePath}
      Code to lint and format:
      \`\`\`
      ${code}
      \`\`\`
      Refine the formatting and apply basic, safe linting corrections to this code. Do not change its logic.
      Return the improved code in the "lintedCode" field. Optionally, provide a brief "explanation".
    `;
    return this.makeRequestWithRetry<GeminiJsonLintFormatResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_LINTER_FORMATTER,
      (data): data is GeminiJsonLintFormatResponse => {
        return typeof data === 'object' && data !== null &&
               'lintedCode' in data && typeof data.lintedCode === 'string' &&
               (data.explanation === null || typeof data.explanation === 'undefined' || typeof data.explanation === 'string');
      },
      0.3,
      true // isCodeGenerationType should be true as output structure is similar ({lintedCode: "..."})
    );
  }

  async analyzeCodeForSecurityVulnerabilities(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonBugReportResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    const originalPrompt = `
      Project Context: ${projectContext}
      ${fileStructurePrompt}
      File Path Being Analyzed for Security: ${filePath}
      Code to analyze:
      \`\`\`
      ${code}
      \`\`\`
      Identify potential security vulnerabilities (e.g., XSS, SQLi, hardcoded secrets). 
      Each vulnerability found MUST be returned as a BugInfo object with 'isSecurityIssue' set to true and appropriate 'severity' (usually 'high' or 'critical').
    `;
    return this.makeRequestWithRetry<GeminiJsonBugReportResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_SECURITY_ANALYST,
      (data): data is GeminiJsonBugReportResponse => {
        return typeof data === 'object' && data !== null &&
               'bugs' in data && Array.isArray(data.bugs) &&
               data.bugs.every(b => 
                 typeof b === 'object' && b !== null && 
                 'filePath' in b && typeof b.filePath === 'string' &&
                 'bugId' in b && typeof b.bugId === 'string' && 
                 'description' in b && typeof b.description === 'string' &&
                 'severity' in b && typeof b.severity === 'string' && 
                 ['low', 'medium', 'high', 'critical'].includes(b.severity) &&
                 (b.isSecurityIssue === true) 
               );
      },
      0.4 
    );
  }

  async analyzeCodeForBugs(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string 
  ): Promise<GeminiJsonBugReportResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    const originalPrompt = `
      Project Context: ${projectContext}
      ${fileStructurePrompt}
      File Path Being Analyzed: ${filePath}
      Code to analyze:
      \`\`\`
      ${code}
      \`\`\`
      Identify potential bugs or issues. Each bug must have a unique 'bugId'.
    `;
    return this.makeRequestWithRetry<GeminiJsonBugReportResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_BUG_HUNTER,
      (data): data is GeminiJsonBugReportResponse => {
        return typeof data === 'object' && data !== null && 
               'bugs' in data && Array.isArray(data.bugs) &&
               data.bugs.every(b => 
                 typeof b === 'object' && b !== null && 
                 'filePath' in b && typeof b.filePath === 'string' &&
                 'bugId' in b && typeof b.bugId === 'string' && 
                 'description' in b && typeof b.description === 'string' &&
                 'severity' in b && typeof b.severity === 'string' && 
                 ['low', 'medium', 'high', 'critical'].includes(b.severity)
               );
      },
      0.4
    );
  }

  async refactorCode(
    code: string, 
    bugDescription: string,
    bugId: string,
    filePath: string, 
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string,
    isSecurityIssue: boolean = false 
  ): Promise<GeminiJsonRefactorResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    const originalPrompt = `
      Project Context: ${projectContext}
      ${fileStructurePrompt}
      Primary File Path With Bug: ${filePath}
      Original Code (for primary file path, if relevant for context, otherwise focus on the bug description):
      \`\`\`
      ${code}
      \`\`\`
      Specific Bug to Fix (ID: ${bugId}): ${bugDescription}
      ${isSecurityIssue ? "\nCRITICAL: This bug is flagged as a SECURITY ISSUE. Prioritize a secure fix." : ""}
      
      Refactor the code to fix ONLY this specific bug. If fixing this bug requires changes in multiple files (e.g., the primary file and related files, or creating new helper files), provide all necessary changes in the 'fileChanges' array.
      If the bug description is too ambiguous to fix confidently, you may ask a 'clarificationQuestion'.
    `;
    return this.makeRequestWithRetry<GeminiJsonRefactorResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_REFACTORER,
      (data): data is GeminiJsonRefactorResponse => {
          return typeof data === 'object' && data !== null &&
                 'fileChanges' in data && Array.isArray(data.fileChanges) &&
                 data.fileChanges.every(fc => 
                   typeof fc === 'object' && fc !== null &&
                   'filePath' in fc && typeof fc.filePath === 'string' &&
                   'fixedCode' in fc && typeof fc.fixedCode === 'string'
                 ) &&
                 ('explanation' in data && typeof data.explanation === 'string') && // Explanation is required by prompt
                 (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
      },
      0.5
    );
  }
  
  async updateProjectContext(
    currentContext: string,
    newInformation: string,
    modelName: string 
  ): Promise<GeminiJsonContextUpdateResponse> {
    const originalPrompt = `
      Current Project Context:
      ---
      ${currentContext}
      ---
      New Information to integrate:
      ---
      ${newInformation}
      ---
      Update the project context.
    `;
    return this.makeRequestWithRetry<GeminiJsonContextUpdateResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_CONTEXT_MANAGER,
      (data): data is GeminiJsonContextUpdateResponse => {
          return typeof data === 'object' && data !== null && 
                 'updatedContext' in data && typeof data.updatedContext === 'string';
      },
      0.3
    );
  }

  // --- Methods for Tester Agent ---

  async generateTestPlan(
    projectContext: string, 
    fileStructure: FileNode[], 
    modelName: string 
  ): Promise<GeminiJsonTestPlanResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    const originalPrompt = `
      Project Context: ${projectContext}
      ${fileStructurePrompt}
      Based on the project context and existing source files, devise a test plan.
      Specify test files to create, their purpose, and related source files.
    `;
    return this.makeRequestWithRetry<GeminiJsonTestPlanResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_TEST_PLANNER,
      (data): data is GeminiJsonTestPlanResponse => {
        return typeof data === 'object' && data !== null &&
               'testFiles' in data && Array.isArray(data.testFiles) &&
               data.testFiles.every((tf: any) => 
                 typeof tf === 'object' && tf !== null &&
                 'filePath' in tf && typeof tf.filePath === 'string' &&
                 'description' in tf && typeof tf.description === 'string' &&
                 'relatedSourceFiles' in tf && Array.isArray(tf.relatedSourceFiles) &&
                 tf.relatedSourceFiles.every((rsf: any) => typeof rsf === 'string')
               ) &&
               (data.overallStrategy === null || typeof data.overallStrategy === 'undefined' || typeof data.overallStrategy === 'string');
      },
      0.4 
    );
  }

  async generateTestCode(
    projectContext: string,
    testFilePath: string,
    testFileDescription: string,
    relatedSourceFiles: string[],
    fileStructure: FileNode[],
    modelName: string 
  ): Promise<GeminiJsonCodeResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure);
    const originalPrompt = `
      Project Context: ${projectContext}
      ${fileStructurePrompt}
      Test File Path To Generate: ${testFilePath}
      Test File Description/Purpose: ${testFileDescription}
      Related Source Files: ${relatedSourceFiles.join(', ')}
      Generate the test code for this file.
    `;
    return this.makeRequestWithRetry<GeminiJsonCodeResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_TEST_CODER, 
       (data): data is GeminiJsonCodeResponse => {
        return typeof data === 'object' && data !== null && 
               'code' in data && typeof data.code === 'string' &&
               (data.clarificationQuestion === null || typeof data.clarificationQuestion === 'undefined' || typeof data.clarificationQuestion === 'string');
      },
      0.5,
      true 
    );
  }

  private getAllFileContents(nodes: FileNode[]): string {
    let allContents = "";
    const traverse = (innerNodes: FileNode[]) => {
        for (const node of innerNodes) {
            if (node.type === 'file') {
                allContents += `\n\n--- FILE: ${node.path} ${node.isTestFile ? '(TEST FILE)' : '(SOURCE FILE)'} ---\n`;
                allContents += node.content || "// This file is empty.";
            }
            if (node.children) {
                traverse(node.children);
            }
        }
    };
    traverse(nodes);
    return allContents;
  }

  // --- Lightweight Test Runner ---
  private createExpect(currentTestResults: { passed: boolean, error?: { message: string, stack?: string }}) {
    const failTest = (message: string) => {
        currentTestResults.passed = false;
        currentTestResults.error = { message };
        throw new Error(message); // Stop further execution in this test
    };

    const expectMatcher = (actual: any) => ({
        toBe: (expected: any) => {
            if (actual !== expected) {
                failTest(`Expected: ${expected}, Actual: ${actual}`);
            }
        },
        toEqual: (expected: any) => {
            try {
                if (JSON.stringify(actual) !== JSON.stringify(expected)) {
                     failTest(`Expected deeply: ${JSON.stringify(expected)}, Actual: ${JSON.stringify(actual)}`);
                }
            } catch (e) { 
                 failTest(`toEqual failed due to JSON.stringify error or direct inequality. Expected: ${expected}, Actual: ${actual}`);
            }
        },
        toBeTruthy: () => {
            if (!actual) {
                failTest(`Expected ${actual} to be truthy`);
            }
        },
        toBeFalsy: () => {
            if (actual) {
                failTest(`Expected ${actual} to be falsy`);
            }
        },
        toBeNull: () => {
            if (actual !== null) {
                failTest(`Expected ${actual} to be null`);
            }
        },
        toBeDefined: () => {
            if (typeof actual === 'undefined') {
                failTest(`Expected ${actual} to be defined`);
            }
        },
        toBeUndefined: () => {
            if (typeof actual !== 'undefined') {
                failTest(`Expected ${actual} to be undefined`);
            }
        },
        toContain: (item: any) => {
            if (Array.isArray(actual) || typeof actual === 'string') {
                if (!actual.includes(item)) {
                    failTest(`Expected ${actual} to contain ${item}`);
                }
            } else {
                failTest(`Expected ${actual} to be an Array or String for toContain`);
            }
        },
        toHaveLength: (length: number) => {
             if (typeof actual?.length !== 'number') {
                failTest(`Expected ${actual} to have a length property`);
             } else if (actual.length !== length) {
                failTest(`Expected length ${length}, Actual: ${actual.length}`);
             }
        },
        toThrow: (expectedErrorMsgSubstring?: string) => {
            if (typeof actual !== 'function') {
                failTest('Expected a function for toThrow assertion.');
                return;
            }
            try {
                actual();
                failTest('Expected function to throw an error, but it did not.');
            } catch (e: any) {
                if (expectedErrorMsgSubstring) {
                    const errorMessage = e.message || String(e);
                    if (!errorMessage.includes(expectedErrorMsgSubstring)) {
                        failTest(`Expected error message to include "${expectedErrorMsgSubstring}", but got "${errorMessage}".`);
                    }
                }
            }
        }
    });
    return expectMatcher;
  }


  private async runTestCode(
    testFileContent: string,
    sourceFilesMap: Record<string, FileNode>, 
    testFilePath: string
  ): Promise<TestResult[]> {
    const results: TestResult[] = [];
    const capturedLogs: string[] = [];

    const projectModules: Record<string, any> = {};

    for (const path in sourceFilesMap) {
        const fileNode = sourceFilesMap[path];
        if (fileNode && fileNode.content && !fileNode.isTestFile) { 
            try {
                const exportsObj: Record<string, any> = {};
                const moduleObj = { exports: exportsObj };
                
                const requireFunc = (modulePath: string): any => {
                    let resolvedPath = modulePath;
                    if (modulePath.startsWith('./')) {
                        const currentDir = testFilePath.substring(0, testFilePath.lastIndexOf('/'));
                        resolvedPath = `${currentDir}/${modulePath.substring(2)}`;
                    } else if (modulePath.startsWith('../')) {
                        const segments = testFilePath.split('/');
                        segments.pop(); 
                        let parentPath = modulePath;
                        while (parentPath.startsWith('../')) {
                            segments.pop(); 
                            parentPath = parentPath.substring(3);
                        }
                        resolvedPath = segments.join('/') + (segments.length > 0 ? '/' : '') + parentPath;
                    }
                    
                    if (projectModules[resolvedPath]) {
                        return projectModules[resolvedPath];
                    }
                    throw new Error(`Simplified Test Runner: Module not found: ${modulePath} (resolved as ${resolvedPath})`);
                };


                const moduleWrapper = new Function('exports', 'module', 'require', fileNode.content);
                moduleWrapper(exportsObj, moduleObj, requireFunc);
                projectModules[path] = moduleObj.exports;

                if (Object.keys(projectModules[path] || {}).length === 0 && fileNode.content.includes('export default')) {
                     const defaultExportMatch = fileNode.content.match(/export\s+default\s+([^;{\n]+)/);
                     if (defaultExportMatch && defaultExportMatch[1]) {
                        try {
                             const tempFunc = new Function('require', `return ${defaultExportMatch[1].trim()}`);
                             projectModules[path] = tempFunc(requireFunc);
                        } catch (evalErr) {
                             console.warn(`Could not evaluate potential default export for ${path}:`, evalErr);
                        }
                     }
                }


            } catch (e) {
                console.error(`Error processing source file ${path} for test environment:`, e);
                projectModules[path] = { error: `Failed to load module ${path}: ${e instanceof Error ? e.message : String(e)}` };
            }
        }
    }
    
    let currentSuiteName = `Suite in ${testFilePath}`;
    const testFunctions: Array<{ name: string; fn: () => void | Promise<void>; suite: string }> = [];

    const describe = (name: string, suiteFn: () => void) => {
        const originalSuiteName = currentSuiteName;
        currentSuiteName = name;
        suiteFn();
        currentSuiteName = originalSuiteName;
    };

    const it = (name: string, testFn: () => void | Promise<void>) => {
        testFunctions.push({ name, fn: testFn, suite: currentSuiteName });
    };
    const test = it; 
    
    const mockRegistry: Record<string, any> = {};
    const jest = {
        fn: (implementation?: (...args: any[]) => any) => {
            const mockFn = (...args: any[]) => {
                (mockFn as any).mock.calls.push(args);
                if (implementation) {
                    return implementation(...args);
                }
                return (mockFn as any).mock.results.length > 0 ? (mockFn as any).mock.results.pop().value : undefined;
            };
            (mockFn as any).mock = {
                calls: [] as any[][],
                instances: [] as any[], 
                results: [] as Array<{ type: string, value: any }>, 
                mockReturnValueOnce: (value: any) => { (mockFn as any).mock.results.unshift({ type: 'return', value }); return jest; },
                mockResolvedValueOnce: (value: any) => { (mockFn as any).mock.results.unshift({ type: 'return', value: Promise.resolve(value) }); return jest; },
                mockRejectedValueOnce: (value: any) => { (mockFn as any).mock.results.unshift({ type: 'return', value: Promise.reject(value) }); return jest; },

            };
            return mockFn;
        },
        mock: (moduleName: string, factory?: () => any) => {
             let resolvedPath = moduleName;
             if (moduleName.startsWith('./')) {
                const currentDir = testFilePath.substring(0, testFilePath.lastIndexOf('/'));
                resolvedPath = `${currentDir}/${moduleName.substring(2)}`;
            } else if (moduleName.startsWith('../')) {
                const segments = testFilePath.split('/');
                segments.pop(); 
                let parentPath = moduleName;
                while (parentPath.startsWith('../')) {
                    segments.pop(); 
                    parentPath = parentPath.substring(3);
                }
                resolvedPath = segments.join('/') + (segments.length > 0 ? '/' : '') + parentPath;
            }

            mockRegistry[resolvedPath] = factory ? factory() : {};
            return jest;
        },
    };


    try {
        const mainTestExecutionScope = {
            describe,
            it,
            test,
            jest, 
            console: { 
                log: (...args: any[]) => capturedLogs.push(args.map(String).join(' ')),
                warn: (...args: any[]) => capturedLogs.push(`WARN: ${args.map(String).join(' ')}`),
                error: (...args: any[]) => capturedLogs.push(`ERROR: ${args.map(String).join(' ')}`),
            },
             require: (modulePath: string): any => { 
                let resolvedPath = modulePath;
                if (modulePath.startsWith('./')) {
                    const currentDirParts = testFilePath.split('/');
                    currentDirParts.pop(); 
                    const currentDir = currentDirParts.join('/');
                    resolvedPath = `${currentDir}/${modulePath.substring(2)}`;
                } else if (modulePath.startsWith('../')) {
                    const pathParts = testFilePath.split('/');
                    pathParts.pop(); 
                    let tempPath = modulePath;
                    while(tempPath.startsWith('../')) {
                        pathParts.pop();
                        tempPath = tempPath.substring(3);
                    }
                    resolvedPath = (pathParts.length > 0 ? pathParts.join('/') + '/' : '') + tempPath;
                }
                
                if (mockRegistry[resolvedPath]) {
                    return mockRegistry[resolvedPath];
                }
                if (projectModules[resolvedPath]) {
                    return projectModules[resolvedPath];
                }
                throw new Error(`Simplified Test Runner (in test scope): Module not found: ${modulePath} (resolved as ${resolvedPath})`);
            }
        };
        const testDefinitionFn = new Function(...Object.keys(mainTestExecutionScope), testFileContent);
        testDefinitionFn(...Object.values(mainTestExecutionScope));

        for (const testCase of testFunctions) {
            const startTime = performance.now();
            const currentTestResultState = { passed: true, error: undefined as ({ message: string; stack?: string } | undefined) };
            
            const expect = this.createExpect(currentTestResultState);

            const individualTestScope = {
                expect,
                console: mainTestExecutionScope.console, 
                require: mainTestExecutionScope.require, 
                jest: mainTestExecutionScope.jest, 
            };
            
            try {
                const testInstanceFn = new Function(...Object.keys(individualTestScope), `return (${testCase.fn.toString()})();`);
                const resultPromise = testInstanceFn(...Object.values(individualTestScope));
                if (resultPromise && typeof resultPromise.then === 'function') {
                    await resultPromise;
                }
            } catch (e: any) {
                 if (currentTestResultState.passed) { 
                    currentTestResultState.passed = false;
                    currentTestResultState.error = { message: e.message || String(e), stack: e.stack };
                }
            }
            const endTime = performance.now();
            results.push({
                suiteName: testCase.suite,
                testName: testCase.name,
                passed: currentTestResultState.passed,
                error: currentTestResultState.error,
                duration: endTime - startTime,
            });
        }

    } catch (e: any) {
        console.error(`Error setting up or running tests in ${testFilePath}:`, e);
        results.push({
            suiteName: `File Level Error in ${testFilePath}`,
            testName: "Test File Execution Error",
            passed: false,
            error: { message: e.message || String(e), stack: e.stack },
            duration: 0,
        });
    }
    if (capturedLogs.length > 0) {
        console.log(`Logs captured during test execution of ${testFilePath}: \n${capturedLogs.join('\n')}`);
    }
    return results;
  }



  async analyzeProjectWithTests( 
    projectContext: string, 
    fileStructure: FileNode[], 
    modelName: string 
  ): Promise<GeminiJsonTestAnalysisResponse> {
    console.log("Initiating actual test execution (Test Runner)...");
    const issues: BugInfo[] = []; 
    let overallSummary = "Test Execution Summary (Local Runner):\n";
    let testsRun = 0;
    let testsFailed = 0;
    
    const testFileNodes = fileStructure.flatMap(node => {
        const collectTestFiles = (n: FileNode): FileNode[] => {
            let files: FileNode[] = [];
            if (n.type === 'file' && n.isTestFile && n.content) {
                files.push(n);
            }
            if (n.children) {
                files = files.concat(...n.children.map(collectTestFiles));
            }
            return files;
        };
        return collectTestFiles(node);
    });
    
    const sourceFilesMap: Record<string, FileNode> = {};
     fileStructure.forEach(node => { 
        const traverse = (n: FileNode) => {
            if (n.type === 'file' && n.content) { 
                sourceFilesMap[n.path] = n;
            }
            if (n.children) n.children.forEach(traverse);
        };
        traverse(node);
    });


    for (const testFileNode of testFileNodes) {
        if (!testFileNode.content) continue;

        console.log(`Executing tests in: ${testFileNode.path}`);
        try {
            const testResults = await this.runTestCode(testFileNode.content, sourceFilesMap, testFileNode.path);
            
            overallSummary += `\n--- Test File: ${testFileNode.path} ---\n`;
            testResults.forEach(result => {
                testsRun++;
                overallSummary += `  ${result.passed ? '✅' : '❌'} [${result.suiteName}] ${result.testName} (${result.duration.toFixed(2)}ms)\n`;
                if (!result.passed) {
                    testsFailed++;
                    const errorMsg = result.error?.message || "Unknown error";
                    overallSummary += `     Error: ${errorMsg}\n`;
                    if (result.error?.stack) {
                         overallSummary += `     Stack: ${result.error.stack.substring(0, 200)}...\n`;
                    }
                    
                    issues.push({
                        filePath: testFileNode.path, 
                        bugId: `test-run-fail-${uuidv4().substring(0,8)}`, 
                        description: `Test Failed: [${result.suiteName}] "${result.testName}". Error: ${errorMsg}`,
                        severity: 'high', 
                        attempts: 0,
                        isSecurityIssue: false,
                        originalTestFailure: { 
                            testFilePath: testFileNode.path,
                            testName: `[${result.suiteName}] ${result.testName}`,
                            errorMessage: errorMsg + (result.error?.stack ? `\nStack: ${result.error.stack}` : '')
                        }
                    });
                }
            });
        } catch (e: any) {
            console.error(`Critical error running test file ${testFileNode.path}:`, e);
            testsRun++;
            testsFailed++;
            const criticalErrorMsg = e.message || String(e);
            issues.push({
                filePath: testFileNode.path,
                bugId: `test-execution-error-${uuidv4().substring(0,8)}`,
                description: `Failed to execute test file ${testFileNode.path}: ${criticalErrorMsg}`,
                severity: 'high',
                attempts: 0,
                isSecurityIssue: false,
                originalTestFailure: {
                    testFilePath: testFileNode.path,
                    testName: "Test File Execution Error",
                    errorMessage: criticalErrorMsg + (e.stack ? `\nStack: ${e.stack}` : '')
                }
            });
            overallSummary += `  ❌ FATAL ERROR executing ${testFileNode.path}: ${criticalErrorMsg}\n`;
        }
    }

    overallSummary += `\nTotal Tests Run: ${testsRun}. Passed: ${testsRun - testsFailed}. Failed: ${testsFailed}.\n`;
    console.log("Test Runner Summary:", overallSummary);

    return Promise.resolve({ 
        issues: issues, 
        summary: overallSummary,
    });
  }

  async analyzeSingleTestFailure(
    failedTestDetails: BugInfo, 
    testFileNode: FileNode,      
    allSourceFiles: FileNode[],  
    projectFullContext: string,
    modelName: string
  ): Promise<GeminiJsonSingleTestFailureAnalysisResponse> {
    if (!failedTestDetails.originalTestFailure) {
        throw new Error("Original test failure details are missing in BugInfo for AI analysis.");
    }
    if (!testFileNode || !testFileNode.content) {
        throw new Error(`Test file node or its content is missing for path: ${failedTestDetails.originalTestFailure.testFilePath}`);
    }

    const relatedSourceFilesContent: { path: string, content: string }[] = [];
    const testFileNameNoExt = testFileNode.name.split('.')[0].toLowerCase();

    allSourceFiles.forEach(sf => {
        if (sf.content && !sf.isTestFile) { 
            if (sf.name.toLowerCase().includes(testFileNameNoExt) || testFileNameNoExt.includes(sf.name.split('.')[0].toLowerCase()) ) {
                 relatedSourceFilesContent.push({ path: sf.path, content: sf.content });
            }
            else if (testFileNode.content && (testFileNode.content.includes(`'${sf.name}'`) || testFileNode.content.includes(`"${sf.name}"`) || testFileNode.content.includes(sf.name.split('.')[0]) )) {
                 relatedSourceFilesContent.push({ path: sf.path, content: sf.content });
            }
        }
    });
    
    if (relatedSourceFilesContent.length === 0) {
        allSourceFiles.filter(sf => !sf.isTestFile && sf.content).slice(0, 3).forEach(sf => { 
            relatedSourceFilesContent.push({ path: sf.path, content: sf.content });
        });
    }
    
    const sourceCodePrompt = relatedSourceFilesContent.map(sf => `--- Source Code: ${sf.path} ---\n${sf.content}\n--- End Source Code: ${sf.path} ---`).join('\n\n');

    const originalPrompt = `
      Project Full Context: ${projectFullContext}
      ---
      Test Failure Details:
      Test File Path: ${failedTestDetails.originalTestFailure.testFilePath}
      Test Name: ${failedTestDetails.originalTestFailure.testName}
      Error Message & Stack (if any): ${failedTestDetails.originalTestFailure.errorMessage}
      ---
      Full Code of the Test File (${testFileNode.path}):
      \`\`\`javascript
      ${testFileNode.content}
      \`\`\`
      ---
      Relevant Source Code Snippets (from files like ${relatedSourceFilesContent.map(sf => sf.path).join(', ') || 'N/A'}):
      ${sourceCodePrompt || "No specific related source files identified; consider general project context and provided file structure."}
      ---
      Analyze this failed test and provide a BugInfo object. 
      The "filePath" in BugInfo MUST be the path to the SOURCE file where the bug most likely resides.
      The "description" should explain the root cause of the bug in the source code, not just repeat the test error.
      The "bugId" should be a new unique ID prefixed with "test-analysis-".
      Include "lineNumber" in BugInfo if you can identify it in the source file.
    `;

    return this.makeRequestWithRetry<GeminiJsonSingleTestFailureAnalysisResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_TEST_ANALYZER,
      (data): data is GeminiJsonSingleTestFailureAnalysisResponse => {
        return typeof data === 'object' && data !== null && 
               'analyzedBugInfo' in data && typeof data.analyzedBugInfo === 'object' && data.analyzedBugInfo !== null &&
               'filePath' in data.analyzedBugInfo && typeof data.analyzedBugInfo.filePath === 'string' &&
               'bugId' in data.analyzedBugInfo && typeof data.analyzedBugInfo.bugId === 'string' &&
               'description' in data.analyzedBugInfo && typeof data.analyzedBugInfo.description === 'string' &&
               'severity' in data.analyzedBugInfo && typeof data.analyzedBugInfo.severity === 'string' &&
               ['low', 'medium', 'high', 'critical'].includes(data.analyzedBugInfo.severity) &&
               (data.analyzedBugInfo.lineNumber === null || typeof data.analyzedBugInfo.lineNumber === 'undefined' || typeof data.analyzedBugInfo.lineNumber === 'number');
      },
      0.5 
    );
  }

  async analyzeTestCoverage(
    projectContext: string, 
    fileStructure: FileNode[], 
    modelName: string
  ): Promise<GeminiJsonTestPlanResponse> {
    const fileStructureWithContentPrompt = this.serializeFileStructureForPrompt(fileStructure, true); 
    
    const originalPrompt = `
      Project Context: ${projectContext}
      ${fileStructureWithContentPrompt}
      Based on the project context, existing source code (including its content), and existing test files (including their content), analyze the current test coverage.
      Identify critical untested areas of the source code. Suggest a few (0 to 3 MAXIMUM) new test files to improve coverage for these critical areas.
      If coverage is deemed reasonably sufficient for the project's complexity and critical paths (considering the content of existing tests), return an empty "testFiles" array.
      Each suggested test file object MUST have "filePath", "description" (why it's important for coverage), and "relatedSourceFiles".
    `;
    return this.makeRequestWithRetry<GeminiJsonTestPlanResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_TEST_COVERAGE_ANALYZER,
      (data): data is GeminiJsonTestPlanResponse => {
        return typeof data === 'object' && data !== null &&
               'testFiles' in data && Array.isArray(data.testFiles) &&
               data.testFiles.every((tf: any) => 
                 typeof tf === 'object' && tf !== null &&
                 'filePath' in tf && typeof tf.filePath === 'string' &&
                 'description' in tf && typeof tf.description === 'string' &&
                 'relatedSourceFiles' in tf && Array.isArray(tf.relatedSourceFiles) &&
                 tf.relatedSourceFiles.every((rsf: any) => typeof rsf === 'string')
               ) &&
               !('overallStrategy' in data && typeof data.overallStrategy !== 'undefined' && data.overallStrategy !== null); 
      },
      0.4 
    );
  }


  async generateImageViaGemini(
    fullPrompt: string,
    modelName: string,
    outputMimeType: 'image/png' | 'image/jpeg' = 'image/png'
  ): Promise<{ dataUrl: string, rawBytes: string, mimeType: string }> {
    let currentAttempt = 0;
    let lastError: Error | RateLimitError | null = null;
    let maxRetriesForCurrentErrorType = this.MAX_GENERAL_RETRIES_FOR_IMAGE_GENERATION;

    while (currentAttempt <= maxRetriesForCurrentErrorType) {
        try {
            if (currentAttempt > 0 && lastError) {
                 console.warn(`Retry ${currentAttempt}/${maxRetriesForCurrentErrorType} for image generation using model ${modelName} due to error: ${lastError.message}. Retrying.`);
            }
            
            const config: {
              numberOfImages: number;
              outputMimeType: 'image/png' | 'image/jpeg';
            } = {
              numberOfImages: 1,
              outputMimeType: outputMimeType
            };
            
            console.log(`Attempting image generation (attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType +1}) for model ${modelName} with prompt: "${fullPrompt.substring(0,100)}..."`);

            const response = await this.ai.models.generateImages({
                model: modelName,
                prompt: fullPrompt,
                config: config,
            });

            if (!response.generatedImages || response.generatedImages.length === 0 || !response.generatedImages[0].image?.imageBytes) {
                console.error("Gemini image generation response missing image data. Full response:", JSON.stringify(response).substring(0, 500));
                throw new Error('Image generation failed: No image data received from API.');
            }

            const base64ImageBytes: string = response.generatedImages[0].image.imageBytes;
            const dataUrl = `data:${outputMimeType};base64,${base64ImageBytes}`;

            console.log(`Image generation for model ${modelName} complete. Image bytes length: ${base64ImageBytes?.length || 0}`);
            return { dataUrl, rawBytes: base64ImageBytes, mimeType: outputMimeType };

        } catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            console.error(`Image generation attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType +1} with model ${modelName} failed:`, err.message);
            lastError = err;

            if (err.message && err.message.includes("429") && (err.message.toLowerCase().includes("resource_exhausted") || err.message.toLowerCase().includes("rate limit"))) {
                maxRetriesForCurrentErrorType = this.RATE_LIMIT_MAX_RETRIES;
                console.warn(`Rate limit detected for image generation model ${modelName}. Max retries for this error type set to ${maxRetriesForCurrentErrorType}.`);
            }
        }
        
        currentAttempt++;
        if (currentAttempt <= maxRetriesForCurrentErrorType) {
            let delay = this.INITIAL_BACKOFF_MS;
            if (lastError && lastError.message && lastError.message.includes("429")) {
                delay = Math.min(this.MAX_BACKOFF_MS, this.INITIAL_BACKOFF_MS * Math.pow(2, currentAttempt -1));
                delay += Math.random() * 1000; 
                console.log(`Image generation rate limit backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
            } else { 
                delay = Math.min(this.MAX_BACKOFF_MS / 2, (this.INITIAL_BACKOFF_MS / 3) * Math.pow(2, currentAttempt -1));
                delay += Math.random() * 500;
                console.log(`Image generation general error backoff for model ${modelName}: waiting ${Math.round(delay/1000)}s before attempt ${currentAttempt + 1}/${maxRetriesForCurrentErrorType + 1}.`);
            }
            await new Promise(resolve => setTimeout(resolve, delay));
        }
    }

    if (lastError instanceof RateLimitError) throw lastError;
    if (lastError?.message.includes("429")) throw new RateLimitError(lastError.message, modelName);
    throw lastError || new Error(`Image generation failed for model ${modelName} after exhausting all ${maxRetriesForCurrentErrorType + 1} retries. Unknown error.`);
  }

  async getClarification(
    question: string,
    projectContext: ProjectContext,
    modelName: string
  ): Promise<GeminiJsonClarifierResponse> {
    const originalPrompt = `
      Full Project Context:
      ---
      Project ID: ${projectContext.id}
      Project Name: ${projectContext.name}
      Original Idea: ${projectContext.idea}
      License: ${projectContext.licenseInfo?.type || 'Unspecified'}
      ${projectContext.licenseInfo?.authorship ? `Author: ${projectContext.licenseInfo.authorship.fullName}` : ''}
      Suggested Technology Stack: ${projectContext.suggestedTechnologyStack || 'Not yet defined'}
      Current Phase: ${projectContext.projectLifecycle}
      Current File Structure Overview:
      ${this.serializeFileStructureForPrompt(projectContext.fileStructure)}
      ---
      Full Context String (long-term memory dump):
      ${projectContext.fullContext.substring(0, 2000)}... 
      ---
      Specific Question from another AI Agent:
      "${question}"
      ---
      Based on all the above context and the question, provide a concise, actionable answer.
      If the information is not explicit, make a reasonable inference or common-sense assumption.
      Avoid saying "I don't know." Your role is to enable the other agent to proceed.
    `;

    return this.makeRequestWithRetry<GeminiJsonClarifierResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_CLARIFIER,
      (data): data is GeminiJsonClarifierResponse => {
        return typeof data === 'object' && data !== null &&
               'answer' in data && typeof data.answer === 'string' &&
               (data.confidence === null || typeof data.confidence === 'undefined' || typeof data.confidence === 'number');
      },
      0.6 
    );
  }

  async validateProjectBuild(
    projectContext: string,
    fileStructure: FileNode[],
    packageJsonContent: string | undefined, 
    modelName: string
  ): Promise<GeminiJsonBuildValidationResponse> {
    const fileStructurePrompt = this.serializeFileStructureForPrompt(fileStructure, false); 
    const originalPrompt = `
      Project Context: ${projectContext}
      ---
      ${fileStructurePrompt}
      ---
      package.json content (if available):
      \`\`\`json
      ${packageJsonContent || "{ \"note\": \"package.json not found or empty\" }"}
      \`\`\`
      ---
      Perform a conceptual build check. 
      Infer project type. Analyze configuration files (package.json, tsconfig.json if implied by TS files). 
      Conceptually check for common type errors (if TypeScript) or Node.js startup issues.
      Report any significant issues that would likely prevent a build or cause runtime failure as "buildIssues".
      Provide "buildCommand", "projectType", and a "validationSummary".
    `;
    return this.makeRequestWithRetry<GeminiJsonBuildValidationResponse>(
      modelName,
      originalPrompt,
      SYSTEM_INSTRUCTION_BUILD_VALIDATOR,
      (data): data is GeminiJsonBuildValidationResponse => {
        return typeof data === 'object' && data !== null &&
               'buildIssues' in data && Array.isArray(data.buildIssues) &&
               data.buildIssues.every(b => 
                 typeof b === 'object' && b !== null && 
                 'filePath' in b && typeof b.filePath === 'string' &&
                 'bugId' in b && typeof b.bugId === 'string' && 
                 'description' in b && typeof b.description === 'string' &&
                 'severity' in b && typeof b.severity === 'string' &&
                 ['low', 'medium', 'high', 'critical'].includes(b.severity)
               ) &&
               (data.buildCommand === null || typeof data.buildCommand === 'undefined' || typeof data.buildCommand === 'string') &&
               (data.projectType === null || typeof data.projectType === 'undefined' || typeof data.projectType === 'string') &&
               'validationSummary' in data && typeof data.validationSummary === 'string';
      },
      0.3
    );
  }
}
