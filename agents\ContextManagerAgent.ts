import { GeminiService } from '../services/geminiService';
import { GeminiJsonContextUpdateResponse } from '../types';

export class ContextManagerAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("ContextManagerAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Updates the project context by integrating new information.
   * @param currentContext - The current project context.
   * @param newInformation - New information to integrate into the context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the updated context response.
   */
  public async updateProjectContext(
    currentContext: string,
    newInformation: string,
    modelName: string
  ): Promise<GeminiJsonContextUpdateResponse> {
    try {
      const response = await this.geminiService.updateProjectContext(
        currentContext,
        newInformation,
        modelName
      );
      return response;
    } catch (error) {
      console.error("ContextManagerAgent: Error updating project context -", error);
      throw error;
    }
  }
}
