import { GeminiService } from '../services/geminiService';
import { GeminiJsonLintFormatResponse } from '../types';

export class LinterFormatterAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("LinterFormatterAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Lints and formats code to improve consistency and readability.
   * @param code - The source code to lint and format.
   * @param filePath - The path of the file being processed.
   * @param projectContext - The current project context.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the lint and format response.
   */
  public async lintAndFormatCode(
    code: string,
    filePath: string,
    projectContext: string,
    modelName: string
  ): Promise<GeminiJsonLintFormatResponse> {
    try {
      const response = await this.geminiService.lintAndFormatCode(
        code,
        filePath,
        projectContext,
        modelName
      );
      return response;
    } catch (error) {
      console.error(`LinterFormatterAgent: Error linting and formatting code for "${filePath}" -`, error);
      throw error;
    }
  }
}
