
import React, { useState, useEffect, useRef, useCallback } from 'react';

interface ResizablePanelsProps {
  children: React.ReactNode[];
  storageKey: string;
  initialSizes?: number[]; // Percentages for each panel
  direction?: 'horizontal' | 'vertical';
  minPanelSizePercent?: number; // Minimum size for any panel in percentage
}

export const ResizablePanels: React.FC<ResizablePanelsProps> = ({
  children,
  storageKey,
  initialSizes: providedInitialSizes,
  direction = 'horizontal',
  minPanelSizePercent = 10,
}) => {
  const [panelSizes, setPanelSizes] = useState<number[]>([]);
  const [draggingSashIndex, setDraggingSashIndex] = useState<number | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const initialMousePosRef = useRef<number>(0);
  const initialSizesRef = useRef<number[]>([]);

  useEffect(() => {
    let defaultInitialSizes = providedInitialSizes;
    if (!defaultInitialSizes || defaultInitialSizes.length !== children.length) {
      console.warn(`ResizablePanels (${storageKey}): Provided initialSizes length does not match children count or is undefined. Falling back to equal distribution.`);
      const equalSize = 100 / children.length;
      defaultInitialSizes = Array(children.length).fill(equalSize);
    } else {
      const sum = defaultInitialSizes.reduce((a, b) => a + b, 0);
      if (Math.abs(sum - 100) > 1) {
        console.warn(`ResizablePanels (${storageKey}): Provided initialSizes do not sum to 100. Normalizing.`);
        defaultInitialSizes = defaultInitialSizes.map(s => (s / sum) * 100);
      }
    }


    const storedSizesRaw = localStorage.getItem(storageKey);
    if (storedSizesRaw) {
      try {
        const storedSizes = JSON.parse(storedSizesRaw) as number[];
        if (
          Array.isArray(storedSizes) &&
          storedSizes.length === children.length &&
          storedSizes.every(s => typeof s === 'number' && s >= minPanelSizePercent && s <= 100) &&
          Math.abs(storedSizes.reduce((a, b) => a + b, 0) - 100) < 1
        ) {
          setPanelSizes(storedSizes);
        } else {
          localStorage.setItem(storageKey, JSON.stringify(defaultInitialSizes));
          setPanelSizes(defaultInitialSizes);
        }
      } catch (error) {
        console.error(`ResizablePanels (${storageKey}): Failed to parse panel sizes. Using defaults.`, error);
        localStorage.setItem(storageKey, JSON.stringify(defaultInitialSizes));
        setPanelSizes(defaultInitialSizes);
      }
    } else {
      localStorage.setItem(storageKey, JSON.stringify(defaultInitialSizes));
      setPanelSizes(defaultInitialSizes);
    }
  }, [storageKey, children.length, minPanelSizePercent, providedInitialSizes]);

  useEffect(() => {
    if (panelSizes.length > 0) { // Only save if panelSizes is initialized
        localStorage.setItem(storageKey, JSON.stringify(panelSizes));
    }
  }, [panelSizes, storageKey]);

  const handleMouseDown = (sashIndex: number, event: React.MouseEvent) => {
    event.preventDefault();
    setDraggingSashIndex(sashIndex);
    initialMousePosRef.current = direction === 'horizontal' ? event.clientX : event.clientY;
    initialSizesRef.current = [...panelSizes];
  };

  const handleMouseMove = useCallback((event: MouseEvent) => {
    if (draggingSashIndex === null || !containerRef.current) return;
    event.preventDefault();

    const currentMousePos = direction === 'horizontal' ? event.clientX : event.clientY;
    const delta = currentMousePos - initialMousePosRef.current;
    const containerSize = direction === 'horizontal' ? containerRef.current.offsetWidth : containerRef.current.offsetHeight;
    if (containerSize === 0) return; // Avoid division by zero
    
    const deltaPercent = (delta / containerSize) * 100;

    let newSizes = [...initialSizesRef.current];
    const panelAIndex = draggingSashIndex;
    const panelBIndex = draggingSashIndex + 1;

    const initialSumOfPair = initialSizesRef.current[panelAIndex] + initialSizesRef.current[panelBIndex];

    let newPanelASize = initialSizesRef.current[panelAIndex] + deltaPercent;
    let newPanelBSize = initialSizesRef.current[panelBIndex] - deltaPercent;

    // Clamp panel A
    newPanelASize = Math.max(minPanelSizePercent, newPanelASize);
    newPanelASize = Math.min(newPanelASize, initialSumOfPair - minPanelSizePercent);
    
    // Panel B takes the remainder of their initial combined size
    newPanelBSize = initialSumOfPair - newPanelASize;

    // Double check panel B clamping (could happen if initialSumOfPair is small)
    newPanelBSize = Math.max(minPanelSizePercent, newPanelBSize);
    newPanelASize = initialSumOfPair - newPanelBSize; // Re-adjust A if B was clamped

    // Final check for A (in case initialSumOfPair was very small)
    newPanelASize = Math.max(minPanelSizePercent, newPanelASize);


    newSizes[panelAIndex] = newPanelASize;
    newSizes[panelBIndex] = newPanelBSize;
    
    // Ensure the sum of all panels is still 100.
    // This simple paired adjustment should maintain the sum if other panels are not involved.
    // For more than 2 panels, this logic might need to redistribute to preserve the sum of all.
    // However, since we only adjust two panels, the sum of *those two* is key.
    // The other panels' sizes are fixed from initialSizesRef during this drag operation.

    setPanelSizes(newSizes);

  }, [draggingSashIndex, minPanelSizePercent, direction, panelSizes]); // Added panelSizes to dep array for safety

  const handleMouseUp = useCallback(() => {
    setDraggingSashIndex(null);
  }, []);

  useEffect(() => {
    if (draggingSashIndex !== null) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = 'none';
    } else {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    }

    return () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      document.body.style.userSelect = '';
    };
  }, [draggingSashIndex, handleMouseMove, handleMouseUp]);

  const containerStyle: React.CSSProperties = {
    display: 'flex',
    width: '100%',
    height: '100%',
    flexDirection: direction === 'horizontal' ? 'row' : 'column',
  };
  
  const panelStyleBase: React.CSSProperties = {
    overflow: 'hidden', // Content within panel should handle its own scroll
    display: 'flex',      // Added for internal flex behavior
    flexDirection: 'column', // Added for internal flex behavior
  };

  const sashBaseStyle: React.CSSProperties = {
    flexShrink: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
    zIndex: 10,
  };

  const sashHandleStyle: React.CSSProperties = 
    direction === 'horizontal' 
    ? { width: '2px', height: '30px', backgroundColor: '#4a5568', borderRadius: '1px' } 
    : { height: '2px', width: '30px', backgroundColor: '#4a5568', borderRadius: '1px' };


  if (panelSizes.length !== children.length) {
      // Still initializing or mismatch, render nothing or a loader
      return null; 
  }

  return (
    <div
      ref={containerRef}
      style={containerStyle}
      className="resizable-panels-container"
    >
      {children.map((child, index) => (
        <React.Fragment key={`panel_fragment_${index}_${storageKey}`}>
          <div
            className={`panel panel-${index}`}
            style={{
              ...panelStyleBase,
              flexBasis: `${panelSizes[index]}%`,
            }}
          >
            {child}
          </div>
          {index < children.length - 1 && (
            <div
              className={`sash ${direction === 'horizontal' ? 'sash-vertical' : 'sash-horizontal'}`}
              style={{
                ...sashBaseStyle,
                cursor: direction === 'horizontal' ? 'col-resize' : 'row-resize',
                width: direction === 'horizontal' ? '8px' : '100%',
                height: direction === 'horizontal' ? '100%' : '8px',
              }}
              onMouseDown={(e) => handleMouseDown(index, e)}
              role="separator"
              aria-orientation={direction}
              aria-controls={`panel-${index} panel-${index + 1}`}
              tabIndex={0}
            >
              <div style={sashHandleStyle}></div>
            </div>
          )}
        </React.Fragment>
      ))}
    </div>
  );
};
