
import { GeminiService } from '../services/geminiService';
import { ProjectContext, GeminiJsonClarifierResponse } from '../types';

export class ClarifierAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("ClarifierAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Provides clarification or makes a decision based on a question and project context.
   * This agent simulates user input or expert knowledge to unblock other agents.
   * @param question - The question posed by another agent.
   * @param projectContext - The current full context of the project.
   * @param modelName - The name of the Gemini model to use for clarification.
   * @returns A promise that resolves to the clarifier's answer.
   */
  public async clarify(
    question: string,
    projectContext: ProjectContext, // Provide the full context
    modelName: string
  ): Promise<GeminiJsonClarifierResponse> {
    try {
      // Call the GeminiService to get clarification from the AI model
      const response = await this.geminiService.getClarification(question, projectContext, modelName);
      return response;
    } catch (error) {
      console.error(`ClarifierAgent: Error during clarification for question "${question}" -`, error);
      // Fallback response in case of error to maintain autonomy
      return {
        answer: `Error in ClarifierAgent: Could not get clarification for question: "${question.substring(0, 100)}...". Reason: ${error instanceof Error ? error.message : 'Unknown error'}. Attempting to proceed with a default or inferred behavior if possible.`,
        confidence: 0.1
      };
    }
  }
}
