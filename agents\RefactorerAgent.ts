import { GeminiService } from '../services/geminiService';
import { GeminiJsonRefactorResponse, FileNode } from '../types';

export class RefactorerAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("RefactorerAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Refactors code to fix a specific bug or issue.
   * @param code - The original source code containing the bug.
   * @param bugDescription - Description of the bug to fix.
   * @param bugId - Unique identifier for the bug.
   * @param filePath - The path of the file containing the bug.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @param isSecurityIssue - Whether the bug is a security issue (default: false).
   * @returns A promise that resolves to the refactor response.
   */
  public async refactorCode(
    code: string,
    bugDescription: string,
    bugId: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string,
    isSecurityIssue: boolean = false
  ): Promise<GeminiJsonRefactorResponse> {
    try {
      const response = await this.geminiService.refactorCode(
        code,
        bugDescription,
        bugId,
        filePath,
        projectContext,
        fileStructure,
        modelName,
        isSecurityIssue
      );
      return response;
    } catch (error) {
      console.error(`RefactorerAgent: Error refactoring code for bug "${bugId}" in "${filePath}" -`, error);
      throw error;
    }
  }
}
