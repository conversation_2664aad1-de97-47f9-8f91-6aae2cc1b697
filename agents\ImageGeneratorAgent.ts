import { GeminiService } from '../services/geminiService';
import { GeneratedImage } from '../types';

export class ImageGeneratorAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("ImageGeneratorAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Generates an image based on a text prompt using the Gemini image generation model.
   * @param prompt - The text prompt describing the image to generate.
   * @param modelName - The name of the Gemini image generation model to use.
   * @returns A promise that resolves to the generated image data.
   */
  public async generateImage(
    prompt: string,
    modelName: string
  ): Promise<GeneratedImage> {
    try {
      const response = await this.geminiService.generateImageViaGemini(
        prompt,
        modelName
      );
      return response;
    } catch (error) {
      console.error(`ImageGeneratorAgent: Error generating image for prompt "${prompt.substring(0, 50)}..." -`, error);
      throw error;
    }
  }
}
