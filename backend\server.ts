import express, { Request, Response, NextFunction } from 'express';
import cors from 'cors';
import { v4 as uuidv4 } from 'uuid';
import { ProjectContext, SavedProjectEntry, AgentLog, Task } from '../types.js';

const app = express();
const port = process.env.BACKEND_PORT || 3002;

app.use(cors());
app.use(express.json({ limit: '50mb' })); // Allow larger payloads for ProjectContext

// In-memory store
let projectsStore: Record<string, ProjectContext> = {};
let companyLogsStore: AgentLog[] = [];

// Helper to create SavedProjectEntry from ProjectContext
const createSavedEntry = (project: ProjectContext): SavedProjectEntry => ({
  id: project.id,
  name: project.name,
  lastModified: project.lastModified,
  ideaSnippet: project.idea.substring(0, 100) + (project.idea.length > 100 ? '...' : ''),
  taskCount: project.tasks.length,
  completedTaskCount: project.tasks.filter((t: Task) => t.status === 'completed').length,
});

// --- Project Endpoints ---
app.get('/api/projects', (req: Request, res: Response) => {
  const savedEntries = Object.values(projectsStore)
    .map(createSavedEntry)
    .sort((a, b) => new Date(b.lastModified).getTime() - new Date(a.lastModified).getTime());
  res.json(savedEntries);
});

app.post('/api/projects', (req: Request, res: Response) => {
  const project = req.body as ProjectContext;
  if (!project.id) {
    project.id = uuidv4(); // Assign an ID if not present
  }
  project.lastModified = new Date().toISOString();
  projectsStore[project.id] = project;
  console.log(`[Backend] Project created/received: ${project.id} - ${project.name}`);
  res.status(201).json({ id: project.id, name: project.name });
});

app.get('/api/projects/:id', (req: Request, res: Response) => {
  const project = projectsStore[req.params.id];
  if (project) {
    res.json(project);
  } else {
    res.status(404).json({ message: 'Project not found' });
  }
});

app.put('/api/projects/:id', (req: Request, res: Response) => {
  const projectId = req.params.id;
  if (!projectsStore[projectId]) {
    // If project doesn't exist, allow POST to create it instead of erroring on PUT
    // Or, handle as a strict update:
    // return res.status(404).json({ message: 'Project not found for update. Use POST to create.' });
  }
  const updatedProject = req.body as ProjectContext;
  updatedProject.id = projectId; // Ensure ID matches
  updatedProject.lastModified = new Date().toISOString();
  projectsStore[projectId] = updatedProject;
  console.log(`[Backend] Project updated: ${projectId} - ${updatedProject.name}`);
  res.json(updatedProject);
});

app.delete('/api/projects/:id', (req: Request, res: Response) => {
  const projectId = req.params.id;
  if (projectsStore[projectId]) {
    const projectName = projectsStore[projectId].name;
    delete projectsStore[projectId];
    console.log(`[Backend] Project deleted: ${projectId} - ${projectName}`);
    res.status(204).send();
  } else {
    res.status(404).json({ message: 'Project not found' });
  }
});

// --- Log Endpoint ---
app.post('/api/logs', (req: Request, res: Response) => {
  const logEntry = req.body as AgentLog;
  if (logEntry && logEntry.id && logEntry.message) {
    companyLogsStore.push(logEntry);
    if (companyLogsStore.length > 5000) { // Keep last 5000 logs
        companyLogsStore = companyLogsStore.slice(-5000);
    }
    // console.log(`[Backend] Log received: ${logEntry.agent} - ${logEntry.message.substring(0,50)}...`);
    res.status(201).json({ message: 'Log received' });
  } else {
    res.status(400).json({ message: 'Invalid log entry' });
  }
});

// Basic error handler
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error("[Backend Error]", err.stack);
  res.status(500).send('Something broke on the backend!');
});

app.listen(port, () => {
  console.log(`[Backend] DevGenius Studio Backend listening on port ${port}`);
  console.log(`[Backend] Available Endpoints:
  GET    /api/projects
  POST   /api/projects
  GET    /api/projects/:id
  PUT    /api/projects/:id
  DELETE /api/projects/:id
  POST   /api/logs`);
});
