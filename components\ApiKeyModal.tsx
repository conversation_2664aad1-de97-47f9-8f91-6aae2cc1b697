
import React, { useState } from 'react';

interface ApiKeyModalProps {
  onSubmit: (apiKey: string) => void;
}

export const ApiKeyModal: React.FC<ApiKeyModalProps> = ({ onSubmit }) => {
  const [apiKey, setApiKey] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (apiKey.trim()) {
      onSubmit(apiKey.trim());
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-900 bg-opacity-75 flex items-center justify-center p-4 z-50">
      <div className="bg-gray-800 p-8 rounded-lg shadow-2xl w-full max-w-md">
        <h2 className="text-2xl font-bold mb-6 text-center text-purple-400">Enter Your Gemini API Key</h2>
        <p className="text-sm text-gray-400 mb-4">
          Your API key is required to use DevGenius Studio. It will be stored locally in your browser's localStorage and
          will not be sent to any server other than Google's Gemini API.
        </p>
        <form onSubmit={handleSubmit}>
          <input
            type="password"
            value={apiKey}
            onChange={(e) => setApiKey(e.target.value)}
            placeholder="Gemini API Key"
            className="w-full p-3 mb-6 bg-gray-700 border border-gray-600 rounded-md text-gray-100 focus:ring-2 focus:ring-purple-500 focus:border-transparent outline-none"
            required
          />
          <button
            type="submit"
            className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-md transition duration-150 ease-in-out"
          >
            Save API Key
          </button>
        </form>
        <p className="text-xs text-gray-500 mt-6 text-center">
          You can obtain a Gemini API key from Google AI Studio.
        </p>
      </div>
    </div>
  );
};
