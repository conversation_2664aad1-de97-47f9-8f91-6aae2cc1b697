import { GeminiService } from '../services/geminiService';
import { GeminiJsonBugReportResponse, FileNode } from '../types';

export class SecurityAnalystAgent {
  private geminiService: GeminiService;

  constructor(geminiService: GeminiService) {
    if (!geminiService) {
      throw new Error("SecurityAnalystAgent: GeminiService instance is required.");
    }
    this.geminiService = geminiService;
  }

  /**
   * Analyzes code for potential security vulnerabilities.
   * @param code - The source code to analyze.
   * @param filePath - The path of the file being analyzed.
   * @param projectContext - The current project context.
   * @param fileStructure - The current file structure of the project.
   * @param modelName - The name of the Gemini model to use.
   * @returns A promise that resolves to the security vulnerability report response.
   */
  public async analyzeCodeForSecurityVulnerabilities(
    code: string,
    filePath: string,
    projectContext: string,
    fileStructure: FileNode[],
    modelName: string
  ): Promise<GeminiJsonBugReportResponse> {
    try {
      const response = await this.geminiService.analyzeCodeForSecurityVulnerabilities(
        code,
        filePath,
        projectContext,
        fileStructure,
        modelName
      );
      return response;
    } catch (error) {
      console.error(`SecurityAnalystAgent: Error analyzing code for security vulnerabilities in "${filePath}" -`, error);
      throw error;
    }
  }
}
